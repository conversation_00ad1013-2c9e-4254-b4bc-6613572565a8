* {
  box-sizing: border-box;
}

body {
  margin: 20px;
  font-size: 14px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #555;
}

input,
.button {
  height: 35px;
  margin: 0;
  padding: 6px 12px;
  border-radius: 2px;
  font-family: inherit;
  font-size: 100%;
  color: inherit;
}

input {
  border: 1px solid #ccc;
  width: 220px;
}

input::placeholder {
  color: #bbb;
}

.button {
  color: #fff;
  background-color: #428bca;
  border: 1px solid #357ebd;
  margin-left: 5px;
}
.button:hover {
  background-color: #3276b1;
  border-color: #285e8e;
  cursor: pointer;
}

.notice {
  margin-top: 15px;
}

@media (prefers-color-scheme: dark) {
  body, input {
    color: white;
    background-color: #0d1117;
  }
  input[disabled] {
    background-color: #3c3c3c;
  }
  input {
    border-color: #5b5b5b;
  }
  input::placeholder {
    color: #8d96a0;
  }
  .iti {
    --iti-border-color: #5b5b5b;
    --iti-dialcode-color: #999999;
    --iti-dropdown-bg: #0d1117;
    --iti-arrow-color: #aaaaaa;
    --iti-hover-color: #30363d;
    --iti-path-globe-1x: url("../img/globe_light.webp");
    --iti-path-globe-2x: url("../img/<EMAIL>");
  }
}
#error-msg {
  color: red;
}

#valid-msg {
  color: #00c900;
}

input.error {
  border: 1px solid #ff7c7c;
}

.hide {
  display: none;
}