const interfaceTranslations = {
  selectedCountryAriaLabel: "<PERSON><PERSON> selecionado",
  noCountrySelected: "Nenhum país selecionado",
  countryListAriaLabel: "Lista de países",
  searchPlaceholder: "Procurar",
  zeroSearchResults: "Nenhum resultado encontrado",
  oneSearchResult: "1 resultado encontrado",
  multipleSearchResults: "${count} resultados encontrados",
  // additional countries (not supported by country-list library)
  ac: "Ilha de Ascensão",
  xk: "Kosovo"
};
export default interfaceTranslations;
