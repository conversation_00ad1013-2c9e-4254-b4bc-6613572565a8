const interfaceTranslations = {
  selectedCountryAriaLabel: "Ausgewähltes Land",
  noCountrySelected: "Kein Land ausgewählt",
  countryListAriaLabel: "Liste der Länder",
  searchPlaceholder: "<PERSON><PERSON>",
  zeroSearchResults: "Keine Suchergebnisse",
  oneSearchResult: "1 Suchergebnis",
  multipleSearchResults: "${count} Suchergebnisse",
  // additional countries (not supported by country-list library)
  ac: "Ascension-Insel",
  xk: "Kosovo"
};
export default interfaceTranslations;
