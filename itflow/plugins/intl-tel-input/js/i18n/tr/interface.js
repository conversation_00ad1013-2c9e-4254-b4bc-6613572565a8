const interfaceTranslations = {
  selectedCountryAriaLabel: "<PERSON><PERSON>ilen ülke",
  noCountrySelected: "Hiçbir ülke seçilmedi",
  countryListAriaLabel: "<PERSON>lk<PERSON> listesi",
  searchPlaceholder: "<PERSON>",
  zeroSearchResults: "<PERSON>u<PERSON> bulunamadı",
  oneSearchResult: "1 sonuç bulundu",
  multipleSearchResults: "${count} sonuç bulundu",
  // additional countries (not supported by country-list library)
  ac: "Ascension Adası",
  xk: "Kosova"
};
export default interfaceTranslations;
