const interfaceTranslations = {
  selectedCountryAriaLabel: "ประเทศที่เลือก",
  noCountrySelected: "ไม่ได้เลือกประเทศ",
  countryListAriaLabel: "รายชื่อประเทศ",
  searchPlaceholder: "ค้นหา",
  zeroSearchResults: "ไม่พบผลลัพธ์",
  oneSearchResult: "พบผลลัพธ์ 1 รายการ",
  multipleSearchResults: "พบผลลัพธ์ ${count} รายการ",
  // additional countries (not supported by country-list library)
  ac: "เกาะแอสเซนชัน",
  xk: "โคโซโว"
};
export default interfaceTranslations;
