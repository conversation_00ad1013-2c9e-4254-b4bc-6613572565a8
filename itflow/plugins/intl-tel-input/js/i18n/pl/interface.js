const interfaceTranslations = {
  selectedCountryAriaLabel: "W<PERSON><PERSON>ny kraj",
  noCountrySelected: "<PERSON>e wybrano kraju",
  countryListAriaLabel: "Lista krajów",
  searchPlaceholder: "<PERSON><PERSON><PERSON>",
  zeroSearchResults: "Nie znaleziono wyników",
  oneSearchResult: "Znaleziono 1 wynik",
  multipleSearchResults: "Znaleziono ${count} ${count > 1 && count < 5 ? 'wyniki' : 'wyników'}",
  // additional countries (not supported by country-list library)
  ac: "Wyspa Wniebowstąpienia",
  xk: "Kosowo"
};
export default interfaceTranslations;
