const interfaceTranslations = {
  selectedCountryAriaLabel: "Pays sélectionné",
  noCountrySelected: "Aucun pays sélectionné",
  countryListAriaLabel: "Liste des pays",
  searchPlaceholder: "Recherche",
  zeroSearchResults: "Aucun résultat trouvé",
  oneSearchResult: "1 résultat trouvé",
  multipleSearchResults: "${count} résultats trouvés",
  // additional countries (not supported by country-list library)
  ac: "Île de l'Ascension",
  xk: "Kosovo"
};
export default interfaceTranslations;
