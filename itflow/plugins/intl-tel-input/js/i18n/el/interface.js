const interfaceTranslations = {
  selectedCountryAriaLabel: "Επιλεγμένη χώρα",
  noCountrySelected: "Δεν έχει επιλεγεί χώρα",
  countryListAriaLabel: "Κατάλο<PERSON><PERSON> χωρών",
  searchPlaceholder: "Αναζήτηση",
  zeroSearchResults: "Δεν βρέθηκαν αποτελέσματα",
  oneSearchResult: "Βρέθηκε 1 αποτέλεσμα",
  multipleSearchResults: "Βρέθηκαν ${count} αποτελέσματα",
  // additional countries (not supported by country-list library)
  ac: "Νησί της Ανάληψης",
  xk: "Κοσσυφοπέδιο"
};
export default interfaceTranslations;
