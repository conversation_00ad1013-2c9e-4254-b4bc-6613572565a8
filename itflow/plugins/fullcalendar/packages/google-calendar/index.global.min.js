/*!
FullCalendar Google Calendar Plugin v6.1.19
Docs & License: https://fullcalendar.io/docs/google-calendar
(c) 2024 Adam Shaw
*/
FullCalendar.GoogleCalendar=function(e,a,t){"use strict";const n={parseMeta(e){let{googleCalendarId:a}=e;return!a&&e.url&&(a=function(e){let a;if(/^[^/]+@([^/.]+\.)*(google|googlemail|gmail)\.com$/.test(e))return e;if((a=/^https:\/\/www.googleapis.com\/calendar\/v3\/calendars\/([^/]*)/.exec(e))||(a=/^https?:\/\/www.google.com\/calendar\/feeds\/([^/]*)/.exec(e)))return decodeURIComponent(a[1]);return null}(e.url)),a?{googleCalendarId:a,googleCalendarApiKey:e.googleCalendarApiKey,googleCalendarApiBase:e.googleCalendarApiBase,extraParams:e.extraParams}:null},fetch(e,n,r){let{dateEnv:o,options:l}=e.context,s=e.eventSource.meta,i=s.googleCalendarApiKey||l.googleCalendarApiKey;if(i){let l=function(e){let a=e.googleCalendarApiBase;a||(a="https://www.googleapis.com/calendar/v3/calendars");return a+"/"+encodeURIComponent(e.googleCalendarId)+"/events"}(s),{extraParams:d}=s,g="function"==typeof d?d():d,c=function(e,a,n,r){let o,l,s;r.canComputeOffset?(l=r.formatIso(e.start),s=r.formatIso(e.end)):(l=t.addDays(e.start,-1).toISOString(),s=t.addDays(e.end,1).toISOString());o=Object.assign(Object.assign({},n||{}),{key:a,timeMin:l,timeMax:s,singleEvents:!0,maxResults:9999}),"local"!==r.timeZone&&(o.timeZone=r.timeZone);return o}(e.range,i,g,o);return t.requestJson("GET",l,c).then(([e,t])=>{var o,l;e.error?r(new a.JsonRequestError("Google Calendar API: "+e.error.message,t)):n({rawEvents:(o=e.items,l=c.timeZone,o.map(e=>function(e,a){let t=e.htmlLink||null;t&&a&&(t=function(e,a){return e.replace(/(\?.*?)?(#|$)/,(e,t,n)=>(t?t+"&":"?")+a+n)}(t,"ctz="+a));return{id:e.id,title:e.summary,start:e.start.dateTime||e.start.date,end:e.end.dateTime||e.end.date,url:t,location:e.location,description:e.description,attachments:e.attachments||[],extendedProps:(e.extendedProperties||{}).shared||{}}}(e,l))),response:t})},r)}r(new Error("Specify a googleCalendarApiKey. See https://fullcalendar.io/docs/google-calendar"))}};const r={googleCalendarApiKey:String},o={googleCalendarApiKey:String,googleCalendarId:String,googleCalendarApiBase:String,extraParams:t.identity};var l=a.createPlugin({name:"@fullcalendar/google-calendar",eventSourceDefs:[n],optionRefiners:r,eventSourceRefiners:o});return a.globalPlugins.push(l),e.default=l,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal);