/*!
FullCalendar Web Component v6.1.19
Docs & License: https://fullcalendar.io/docs/web-component
(c) 2024 Adam Shaw
*/
!function(t){"use strict";class e extends HTMLElement{constructor(){super(...arguments),this._calendar=null,this._options=null}connectedCallback(){this._handleOptionsStr(this.getAttribute("options"))}disconnectedCallback(){this._handleOptionsStr(null)}attributeChangedCallback(t,e,n){"options"===t&&this._calendar&&this._handleOptionsStr(n)}get options(){return this._options}set options(t){this._handleOptions(t)}getApi(){return this._calendar}_handleOptionsStr(t){this._handleOptions(t?JSON.parse(t):null)}_handleOptions(e){if(e){if(this._calendar)this._calendar.resetOptions(e);else{let n;this.hasAttribute("shadow")?(this.attachShadow({mode:"open"}),n=this.shadowRoot):n=this,n.innerHTML="<div></div>";let s=n.querySelector("div"),i=new t.Calendar(s,e);i.render(),this._calendar=i}this._options=e}else this._calendar&&(this._calendar.destroy(),this._calendar=null),this._options=null}static get observedAttributes(){return["options"]}}globalThis.FullCalendarElement=e,customElements.define("full-calendar",e)}(FullCalendar);