/*!
FullCalendar List View Plugin v6.1.19
Docs & License: https://fullcalendar.io/docs/list-view
(c) 2024 Adam Shaw
*/
FullCalendar.List=function(e,t,n,a){"use strict";class r extends n.BaseComponent{constructor(){super(...arguments),this.state={textId:n.getUniqueDomId()}}render(){let{theme:e,dateEnv:t,options:r,viewApi:s}=this.context,{cellId:l,dayDate:o,todayRange:d}=this.props,{textId:c}=this.state,f=n.getDateMeta(o,d),u=r.listDayFormat?t.format(o,r.listDayFormat):"",g=r.listDaySideFormat?t.format(o,r.listDaySideFormat):"",m=Object.assign({date:t.toDate(o),view:s,textId:c,text:u,sideText:g,navLinkAttrs:n.buildNavLinkAttrs(this.context,o),sideNavLinkAttrs:n.buildNavLinkAttrs(this.context,o,"day",!1)},f);return a.createElement(n.ContentContainer,{elTag:"tr",elClasses:["fc-list-day",...n.getDayClassNames(f,e)],elAttrs:{"data-date":n.formatDayString(o)},renderProps:m,generatorName:"dayHeaderContent",customGenerator:r.dayHeaderContent,defaultGenerator:i,classNameGenerator:r.dayHeaderClassNames,didMount:r.dayHeaderDidMount,willUnmount:r.dayHeaderWillUnmount},t=>a.createElement("th",{scope:"colgroup",colSpan:3,id:l,"aria-labelledby":c},a.createElement(t,{elTag:"div",elClasses:["fc-list-day-cushion",e.getClass("tableCellShaded")]})))}}function i(e){return a.createElement(a.Fragment,null,e.text&&a.createElement("a",Object.assign({id:e.textId,className:"fc-list-day-text"},e.navLinkAttrs),e.text),e.sideText&&a.createElement("a",Object.assign({"aria-hidden":!0,className:"fc-list-day-side-text"},e.sideNavLinkAttrs),e.sideText))}const s=n.createFormatter({hour:"numeric",minute:"2-digit",meridiem:"short"});class l extends n.BaseComponent{render(){let{props:e,context:t}=this,{options:r}=t,{seg:i,timeHeaderId:l,eventHeaderId:d,dateHeaderId:c}=e,f=r.eventTimeFormat||s;return a.createElement(n.EventContainer,Object.assign({},e,{elTag:"tr",elClasses:["fc-list-event",i.eventRange.def.url&&"fc-event-forced-url"],defaultGenerator:()=>function(e,t){let r=n.getSegAnchorAttrs(e,t);return a.createElement("a",Object.assign({},r),e.eventRange.def.title)}(i,t),seg:i,timeText:"",disableDragging:!0,disableResizing:!0}),(e,r)=>a.createElement(a.Fragment,null,function(e,t,r,i,s){let{options:l}=r;if(!1!==l.displayEventTime){let d,c=e.eventRange.def,f=e.eventRange.instance,u=!1;if(c.allDay?u=!0:n.isMultiDayRange(e.eventRange.range)?e.isStart?d=n.buildSegTimeText(e,t,r,null,null,f.range.start,e.end):e.isEnd?d=n.buildSegTimeText(e,t,r,null,null,e.start,f.range.end):u=!0:d=n.buildSegTimeText(e,t,r),u){let e={text:r.options.allDayText,view:r.viewApi};return a.createElement(n.ContentContainer,{elTag:"td",elClasses:["fc-list-event-time"],elAttrs:{headers:`${i} ${s}`},renderProps:e,generatorName:"allDayContent",customGenerator:l.allDayContent,defaultGenerator:o,classNameGenerator:l.allDayClassNames,didMount:l.allDayDidMount,willUnmount:l.allDayWillUnmount})}return a.createElement("td",{className:"fc-list-event-time"},d)}return null}(i,f,t,l,c),a.createElement("td",{"aria-hidden":!0,className:"fc-list-event-graphic"},a.createElement("span",{className:"fc-list-event-dot",style:{borderColor:r.borderColor||r.backgroundColor}})),a.createElement(e,{elTag:"td",elClasses:["fc-list-event-title"],elAttrs:{headers:`${d} ${c}`}})))}}function o(e){return e.text}class d extends n.DateComponent{constructor(){super(...arguments),this.computeDateVars=n.memoize(f),this.eventStoreToSegs=n.memoize(this._eventStoreToSegs),this.state={timeHeaderId:n.getUniqueDomId(),eventHeaderId:n.getUniqueDomId(),dateHeaderIdRoot:n.getUniqueDomId()},this.setRootEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e}):this.context.unregisterInteractiveComponent(this)}}render(){let{props:e,context:t}=this,{dayDates:r,dayRanges:i}=this.computeDateVars(e.dateProfile),s=this.eventStoreToSegs(e.eventStore,e.eventUiBases,i);return a.createElement(n.ViewContainer,{elRef:this.setRootEl,elClasses:["fc-list",t.theme.getClass("table"),!1!==t.options.stickyHeaderDates?"fc-list-sticky":""],viewSpec:t.viewSpec},a.createElement(n.Scroller,{liquid:!e.isHeightAuto,overflowX:e.isHeightAuto?"visible":"hidden",overflowY:e.isHeightAuto?"visible":"auto"},s.length>0?this.renderSegList(s,r):this.renderEmptyMessage()))}renderEmptyMessage(){let{options:e,viewApi:t}=this.context,r={text:e.noEventsText,view:t};return a.createElement(n.ContentContainer,{elTag:"div",elClasses:["fc-list-empty"],renderProps:r,generatorName:"noEventsContent",customGenerator:e.noEventsContent,defaultGenerator:c,classNameGenerator:e.noEventsClassNames,didMount:e.noEventsDidMount,willUnmount:e.noEventsWillUnmount},e=>a.createElement(e,{elTag:"div",elClasses:["fc-list-empty-cushion"]}))}renderSegList(e,t){let{theme:i,options:s}=this.context,{timeHeaderId:o,eventHeaderId:d,dateHeaderIdRoot:c}=this.state,f=function(e){let t,n,a=[];for(t=0;t<e.length;t+=1)n=e[t],(a[n.dayIndex]||(a[n.dayIndex]=[])).push(n);return a}(e);return a.createElement(n.NowTimer,{unit:"day"},(e,u)=>{let g=[];for(let i=0;i<f.length;i+=1){let m=f[i];if(m){let f=n.formatDayString(t[i]),h=c+"-"+f;g.push(a.createElement(r,{key:f,cellId:h,dayDate:t[i],todayRange:u})),m=n.sortEventSegs(m,s.eventOrder);for(let t of m)g.push(a.createElement(l,Object.assign({key:f+":"+t.eventRange.instance.instanceId,seg:t,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,timeHeaderId:o,eventHeaderId:d,dateHeaderId:h},n.getSegMeta(t,u,e))))}}return a.createElement("table",{className:"fc-list-table "+i.getClass("table")},a.createElement("thead",null,a.createElement("tr",null,a.createElement("th",{scope:"col",id:o},s.timeHint),a.createElement("th",{scope:"col","aria-hidden":!0}),a.createElement("th",{scope:"col",id:d},s.eventHint))),a.createElement("tbody",null,g))})}_eventStoreToSegs(e,t,a){return this.eventRangesToSegs(n.sliceEventStore(e,t,this.props.dateProfile.activeRange,this.context.options.nextDayThreshold).fg,a)}eventRangesToSegs(e,t){let n=[];for(let a of e)n.push(...this.eventRangeToSegs(a,t));return n}eventRangeToSegs(e,t){let a,r,i,{dateEnv:s}=this.context,{nextDayThreshold:l}=this.context.options,o=e.range,d=e.def.allDay,c=[];for(a=0;a<t.length;a+=1)if(r=n.intersectRanges(o,t[a]),r&&(i={component:this,eventRange:e,start:r.start,end:r.end,isStart:e.isStart&&r.start.valueOf()===o.start.valueOf(),isEnd:e.isEnd&&r.end.valueOf()===o.end.valueOf(),dayIndex:a},c.push(i),!i.isEnd&&!d&&a+1<t.length&&o.end<s.add(t[a+1].start,l))){i.end=o.end,i.isEnd=!0;break}return c}}function c(e){return e.text}function f(e){let t=n.startOfDay(e.renderRange.start),a=e.renderRange.end,r=[],i=[];for(;t<a;)r.push(t),i.push({start:t,end:n.addDays(t,1)}),t=n.addDays(t,1);return{dayDates:r,dayRanges:i}}const u={listDayFormat:g,listDaySideFormat:g,noEventsClassNames:n.identity,noEventsContent:n.identity,noEventsDidMount:n.identity,noEventsWillUnmount:n.identity};function g(e){return!1===e?null:n.createFormatter(e)}n.injectStyles(':root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:"";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}');var m=t.createPlugin({name:"@fullcalendar/list",optionRefiners:u,views:{list:{component:d,buttonTextKey:"list",listDayFormat:{month:"long",day:"numeric",year:"numeric"}},listDay:{type:"list",duration:{days:1},listDayFormat:{weekday:"long"}},listWeek:{type:"list",duration:{weeks:1},listDayFormat:{weekday:"long"},listDaySideFormat:{month:"long",day:"numeric",year:"numeric"}},listMonth:{type:"list",duration:{month:1},listDaySideFormat:{weekday:"long"}},listYear:{type:"list",duration:{year:1},listDaySideFormat:{weekday:"long"}}}}),h={__proto__:null,ListView:d};return t.globalPlugins.push(m),e.Internal=h,e.default=m,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal,FullCalendar.Preact);